{"server": {"host": "localhost", "port": "8080"}, "database": {"file": "./users.db"}, "jwt_token": {"secret": "your-jwt-secret-key-here-make-it-long-and-secure", "access_token_expiry_minutes": 15, "refresh_token_expiry_hours": 25}, "admins": ["<EMAIL>", "<EMAIL>"], "mailSender": {"email": "<EMAIL>", "sendgrid_key": "your-sendgrid-api-key-here", "timeout": 5, "max_concurrent_sends": 20, "max_attachment_size_mb": 10}, "currency": "USD", "stripe_secret": "sk_test_your_stripe_secret_key_here", "voucher_name_length": 8, "gridproxy_url": "https://gridproxy.grid.tf", "tfchain_url": "wss://tfchain.grid.tf/ws", "terms_and_conditions": {"document_link": "https://example.com/terms", "document_hash": "sha256:example_hash_here"}, "activation_service_url": "https://activation.grid.tf", "graphql_url": "https://graphql.grid.tf/graphql", "firesquid_url": "https://squid.grid.tf/graphql", "system_account": {"mnemonics": "your system account 12-word mnemonic phrase goes here for blockchain operations", "network": "dev"}, "redis": {"host": "localhost", "port": 6379, "password": "", "db": 0}, "deployer_workers_num": 3, "invoice": {"name": "Your Company Name", "address": "123 Business Street, City", "governorate": "Your Governorate"}, "ssh": {"private_key_path": "/home/<USER>/.ssh/id_rsa", "public_key_path": "/home/<USER>/.ssh/id_rsa.pub"}, "debug": false, "kyc_verifier_api_url": "kyc-url", "kyc_challenge_domain": "kyc-domain"}