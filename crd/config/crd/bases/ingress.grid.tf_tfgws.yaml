---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.18.0
  name: tfgws.ingress.grid.tf
spec:
  group: ingress.grid.tf
  names:
    kind: TFGW
    listKind: TFGWList
    plural: tfgws
    singular: tfgw
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.hostname
      name: Host
      type: string
    - jsonPath: .spec.backends
      name: Backends
      type: string
    - jsonPath: .status.fqdn
      name: FQDN
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: TFGW is the Schema for the tfgws API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: TFGWSpec defines the desired state of TFGW.
            properties:
              backends:
                items:
                  type: string
                type: array
              foo:
                description: Foo is an example field of TFGW. Edit tfgw_types.go to
                  remove/update
                type: string
              hostname:
                type: string
            required:
            - backends
            - hostname
            type: object
          status:
            description: TFGWStatus defines the observed state of TFGW.
            properties:
              fqdn:
                type: string
              message:
                type: string
            required:
            - fqdn
            - message
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
