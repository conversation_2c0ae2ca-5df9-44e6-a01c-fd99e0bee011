# This rule is not used by the project crd itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants read-only access to ingress.grid.tf resources.
# This role is intended for users who need visibility into these resources
# without permissions to modify them. It is ideal for monitoring purposes and limited-access viewing.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: crd
    app.kubernetes.io/managed-by: kustomize
  name: tfgw-viewer-role
rules:
- apiGroups:
  - ingress.grid.tf
  resources:
  - tfgws
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ingress.grid.tf
  resources:
  - tfgws/status
  verbs:
  - get
