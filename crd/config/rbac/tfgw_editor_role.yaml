# This rule is not used by the project crd itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants permissions to create, update, and delete resources within the ingress.grid.tf.
# This role is intended for users who need to manage these resources
# but should not control RBAC or manage permissions for others.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: crd
    app.kubernetes.io/managed-by: kustomize
  name: tfgw-editor-role
rules:
- apiGroups:
  - ingress.grid.tf
  resources:
  - tfgws
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ingress.grid.tf
  resources:
  - tfgws/status
  verbs:
  - get
