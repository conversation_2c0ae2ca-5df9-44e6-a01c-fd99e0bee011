# This rule is not used by the project crd itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants full permissions ('*') over ingress.grid.tf.
# This role is intended for users authorized to modify roles and bindings within the cluster,
# enabling them to delegate specific permissions to other users or groups as needed.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: crd
    app.kubernetes.io/managed-by: kustomize
  name: tfgw-admin-role
rules:
- apiGroups:
  - ingress.grid.tf
  resources:
  - tfgws
  verbs:
  - '*'
- apiGroups:
  - ingress.grid.tf
  resources:
  - tfgws/status
  verbs:
  - get
