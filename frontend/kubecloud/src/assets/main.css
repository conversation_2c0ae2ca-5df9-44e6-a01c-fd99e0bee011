/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Import Shared Component Styles */
@import '../styles/components.css';

/* Linear-Style Design System for KubeCloud */
:root {
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  
  /* KubeCloud Color Palette - Refined for Linear-style */
  --color-primary: #3B82F6;
  --color-primary-light: #60A5FA;
  --color-primary-dark: #1E40AF;
  --color-primary-subtle: rgba(59, 130, 246, 0.1);
  
  --color-secondary: #EA580C;
  --color-secondary-light: #FB923C;
  --color-secondary-dark: #C2410C;
  --color-secondary-subtle: rgba(234, 88, 12, 0.1);
  
  --color-accent: #0891B2;
  --color-accent-light: #22D3EE;
  --color-accent-dark: #0E7490;
  --color-accent-subtle: rgba(8, 145, 178, 0.1);
  
  /* Neutral Colors - Linear-style */
  --color-bg: #0F172A;
  --color-bg-elevated: #1E293B;
  --color-bg-hover: #334155;
  --color-bg-active: #475569;
  
  --color-border: #334155;
  --color-border-light: #475569;
  --color-border-focus: #3B82F6;
  
  --color-text: #F8FAFC;
  --color-text-secondary: #CBD5E1;
  --color-text-muted: #94A3B8;
  --color-text-disabled: #64748B;
  
  /* Semantic Colors */
  --color-success: #10B981;
  --color-success-light: #34D399;
  --color-success-subtle: rgba(16, 185, 129, 0.1);
  
  --color-warning: #F59E0B;
  --color-warning-light: #FBBF24;
  --color-warning-subtle: rgba(245, 158, 11, 0.1);
  
  --color-error: #EF4444;
  --color-error-light: #F87171;
  --color-error-subtle: rgba(239, 68, 68, 0.1);
  
  --color-info: #3B82F6;
  --color-info-light: #60A5FA;
  --color-info-subtle: rgba(59, 130, 246, 0.1);
  
  /* Shadows - Linear-style subtle shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Focus Shadows */
  --shadow-focus: 0 0 0 3px rgba(59, 130, 246, 0.1);
  --shadow-focus-error: 0 0 0 3px rgba(239, 68, 68, 0.1);
  
  /* Transitions - Linear-style smooth transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Global Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--color-bg);
  color: var(--color-text);
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin: 0 0 var(--space-4) 0;
  color: var(--color-text);
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
}

h2 {
  font-size: var(--font-size-3xl);
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
}

h5 {
  font-size: var(--font-size-lg);
}

h6 {
  font-size: var(--font-size-base);
}

p {
  margin: 0 0 var(--space-4) 0;
  color: var(--color-text-secondary);
}

/* Utility Classes */
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-primary { color: var(--color-text); }
.text-secondary { color: var(--color-text-secondary); }
.text-muted { color: var(--color-text-muted); }
.text-disabled { color: var(--color-text-disabled); }

/* Spacing Utilities */
.space-y-1 > * + * { margin-top: var(--space-1); }
.space-y-2 > * + * { margin-top: var(--space-2); }
.space-y-3 > * + * { margin-top: var(--space-3); }
.space-y-4 > * + * { margin-top: var(--space-4); }
.space-y-5 > * + * { margin-top: var(--space-5); }
.space-y-6 > * + * { margin-top: var(--space-6); }
.space-y-8 > * + * { margin-top: var(--space-8); }

.space-x-1 > * + * { margin-left: var(--space-1); }
.space-x-2 > * + * { margin-left: var(--space-2); }
.space-x-3 > * + * { margin-left: var(--space-3); }
.space-x-4 > * + * { margin-left: var(--space-4); }
.space-x-5 > * + * { margin-left: var(--space-5); }
.space-x-6 > * + * { margin-left: var(--space-6); }
.space-x-8 > * + * { margin-left: var(--space-8); }

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-5 { gap: var(--space-5); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

/* Border Radius Utilities */
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-3xl { border-radius: var(--radius-3xl); }

/* Shadow Utilities */
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Transition Utilities */
.transition-all { transition: all var(--transition-normal); }
.transition-colors { transition: color var(--transition-normal), background-color var(--transition-normal), border-color var(--transition-normal); }
.transition-shadow { transition: box-shadow var(--transition-normal); }
.transition-transform { transition: transform var(--transition-normal); }

/* Focus Styles */
.focus-ring:focus {
  outline: none;
  box-shadow: var(--shadow-focus);
}

.focus-ring-error:focus {
  outline: none;
  box-shadow: var(--shadow-focus-error);
}

/* Responsive Design */
@media (max-width: 768px) {
  :root {
    --space-4: 0.75rem;
    --space-6: 1rem;
    --space-8: 1.5rem;
    --space-10: 2rem;
    --space-12: 2.5rem;
  }
  
  h1 { font-size: var(--font-size-3xl); }
  h2 { font-size: var(--font-size-2xl); }
  h3 { font-size: var(--font-size-xl); }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --color-border: #475569;
    --color-border-light: #64748B;
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
  }
}

/* Container Utility for Consistent Alignment */
.container {
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 2rem;
  padding-right: 2rem;
}

@media (max-width: 900px) {
  .container {
    max-width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (max-width: 600px) {
  .container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
