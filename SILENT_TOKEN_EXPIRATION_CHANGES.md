# Silent Token Expiration Handling Implementation

## Problem
Users were experiencing multiple error notifications when their authentication tokens expired, creating a poor user experience. The errors appeared as multiple toast notifications showing "Invalid or expired token" messages.

## Root Cause
Multiple sources were triggering error notifications when tokens expired:
1. **API requests** with expired tokens showing error notifications
2. **Token refresh failures** triggering additional error messages
3. **Global error handlers** catching and displaying errors
4. **Multiple simultaneous API calls** all failing and showing separate notifications
5. **SSE connections** failing due to expired tokens

## Solution Overview
Implemented a **silent token expiration handling system** that:
- Detects token expiration scenarios specifically
- Prevents error notifications for token-related failures
- Silently logs out users and redirects to sign-in page
- Preserves error notifications for legitimate application errors

## Changes Made

### 1. Enhanced API Error Interface (`frontend/kubecloud/src/utils/api.ts`)
```typescript
export interface ApiError {
  message: string
  status?: number
  code?: string
  silent?: boolean  // NEW: Flag to suppress notifications
}
```

### 2. Silent Token Expiration in API Client
**Location**: `frontend/kubecloud/src/utils/api.ts`

**Changes**:
- When 401/403 errors occur and token refresh fails, throw a silent error
- Check for `silent` flag before showing error notifications
- Preserve silent flag when re-throwing errors

**Key Code**:
```typescript
// Silent logout and redirect for token expiration
userStore.logout()
router.push('/sign-in')
throw {
  message: 'Token expired',
  status: 401,
  code: 'TOKEN_EXPIRED',
  silent: true  // Prevents notifications
} as ApiError & { silent: boolean }
```

### 3. Silent Token Refresh Failures (`frontend/kubecloud/src/stores/user.ts`)
**Changes**:
- Modified `refreshToken()` method to throw silent errors on failure
- Prevents error notifications when refresh tokens expire

**Key Code**:
```typescript
catch (err) {
  logout()
  throw {
    message: 'Token refresh failed',
    silent: true  // Prevents notifications
  }
}
```

### 4. Updated Global Error Handlers (`frontend/kubecloud/src/App.vue`)
**Changes**:
- Modified `onErrorCaptured` to respect silent errors
- Updated window error handler to check for silent flag

**Key Code**:
```typescript
onErrorCaptured((error: Error & { silent?: boolean }) => {
  if (!error.silent) {
    notificationStore.error(/* show error */)
  }
  return false
})
```

### 5. Enhanced SSE Error Handling (`frontend/kubecloud/src/composables/useDeploymentEvents.ts`)
**Changes**:
- Improved SSE error handling to detect auth-related connection closures
- Prevents unnecessary reconnection attempts when tokens expire

## User Experience Improvements

### Before
- ❌ Multiple error notifications appearing simultaneously
- ❌ Confusing "Invalid or expired token" messages
- ❌ Poor UX during token expiration
- ❌ Users had to manually dismiss multiple error toasts

### After
- ✅ Silent token expiration handling
- ✅ Automatic redirect to sign-in page
- ✅ No error notifications for token expiration
- ✅ Clean, seamless authentication flow
- ✅ Error notifications preserved for legitimate errors

## Testing
Created comprehensive test suite (`frontend/kubecloud/src/utils/__tests__/silent-token-expiration.test.ts`) covering:
- Silent token expiration scenarios
- Successful token refresh flows
- Non-token error handling (should still show notifications)

## Backward Compatibility
- ✅ All existing error handling preserved for non-token errors
- ✅ No breaking changes to existing API
- ✅ Silent flag is optional and defaults to showing notifications

## Security Considerations
- ✅ Users are still logged out when tokens expire
- ✅ Automatic redirect to sign-in page maintains security
- ✅ No sensitive information exposed in silent errors
- ✅ Token refresh attempts still occur before logout

## Implementation Notes
1. **Silent Flag Propagation**: The `silent` flag is preserved through the entire error handling chain
2. **Selective Suppression**: Only token-related errors are silenced; all other errors show notifications
3. **Graceful Degradation**: If silent flag is missing, default behavior shows notifications
4. **Router Integration**: Automatic redirect to sign-in page using Vue Router
5. **Store Integration**: Proper cleanup of user state and tokens

## Future Enhancements
- Consider adding a subtle, non-intrusive indicator when silent logout occurs
- Implement session timeout warnings before tokens expire
- Add analytics tracking for silent logout events
