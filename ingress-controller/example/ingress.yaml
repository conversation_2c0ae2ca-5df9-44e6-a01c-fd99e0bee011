apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: my-app-ingress
spec:
  ingressClassName: "my-custom-ingress"
  rules:
  - host: externalapp
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: my-app-svc
            port:
              number: 30080

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: my-custom-ingress-sa

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: my-custom-ingress-crb
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: my-custom-ingress-sa
  namespace: default

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-custom-ingress-controller
spec:
  replicas: 1
  selector:
    matchLabels:
      app: my-custom-ingress-controller
  template:
    metadata:
      labels:
        app: my-custom-ingress-controller
    spec:
      serviceAccountName: my-custom-ingress-sa
      containers:
      - name: controller
        image: omarabdul3ziz/tfgw-ingress-controller:latest
        env:
        - name: THREEFOLD_NETWORK
          value: "dev"
        - name: THREEFOLD_MNEMONIC
          valueFrom:
            secretKeyRef:
              name: threefold-credentials
              key: mnemonic
        args:
        - "--v=2"
        - "--logtostderr"
        ports:
        - containerPort: 8080
          name: metrics

---
apiVersion: v1
kind: Secret
metadata:
  name: threefold-credentials
type: Opaque
stringData:
  # Raw mnemonic phrase 
  mnemonic: ""
