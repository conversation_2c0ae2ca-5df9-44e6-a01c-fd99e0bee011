apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: my-app
  template:
    metadata:
      labels:
        app: my-app
    spec:
      containers:
      - name: app
        image: nginx
        ports:
        - containerPort: 80

---
apiVersion: v1
kind: Service
metadata:
  name: my-app-svc
spec:
  type: NodePort
  externalIPs:
  -  ************** 
  selector:
    app: my-app
  ports:
  - port: 80
    targetPort: 80
    nodePort: 30080
